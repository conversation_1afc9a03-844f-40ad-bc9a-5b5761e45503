namespace ThuneeAPI.Models
{
    public class Player
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public bool IsHost { get; set; }
        public int Team { get; set; }
        public bool IsReady { get; set; }
        public bool IsDealer { get; set; }
        public bool IsTrumpSelector { get; set; }
        public int Position { get; set; }
    }

    public class Card
    {
        public string Id { get; set; } = string.Empty;
        public string Suit { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public int Points { get; set; }
        public string Image { get; set; } = string.Empty;
        public string? PlayedBy { get; set; }
    }

    public class Lobby
    {
        public string LobbyCode { get; set; } = string.Empty;
        public string PartnerInviteCode { get; set; } = string.Empty;
        public string OpponentInviteCode { get; set; } = string.Empty;
        public List<Player> Players { get; set; } = new();
        public bool GameStarted { get; set; }
        public Dictionary<int, List<Player>> Teams { get; set; } = new();
        public Dictionary<int, string> TeamNames { get; set; } = new();
        public Dictionary<int, bool> TeamReady { get; set; } = new();
        public bool IsFindingMatch { get; set; }
        public string? MatchedLobby { get; set; }
        public bool ReadyToStart { get; set; }
        public TimeSettings? TimeSettings { get; set; }
        public string? HostId { get; set; }
        public GameState GameState { get; set; } = new();
        public string? DealerId { get; set; }
        public string? TrumpSelectorId { get; set; }
        public string? TrumpSuit { get; set; }
        public Dictionary<string, List<Card>> PlayerCards { get; set; } = new();
        public Dictionary<string, int> BallScores { get; set; } = new();
        public List<Hand> Hands { get; set; } = new();
        public int CurrentHandId { get; set; }
        public int CurrentBallId { get; set; }
        public Dictionary<string, int> BallPoints { get; set; } = new();
        public List<JordhiCall> JordhiCalls { get; set; } = new();
        public List<KhanakCall> KhanakCalls { get; set; } = new();
        public List<DoubleCall> DoubleCalls { get; set; } = new();
        public List<ThuneeCall> ThuneeCalls { get; set; } = new();
        public List<FourBallCall> FourBallCalls { get; set; } = new();
        public bool DealerFound { get; set; }
        public bool DealerDeterminationInProgress { get; set; }
        public int CurrentDealerPlayerIndex { get; set; }
        public List<Card> DealerDeck { get; set; } = new();
        public Dictionary<string, int> CardsDealtPerPlayer { get; set; } = new();
        public List<Card> GameDeck { get; set; } = new();
        public List<Card> RemainingDeck { get; set; } = new();
        public HashSet<string> AllDealtCards { get; set; } = new();
        public TimeframeVoting? TimeframeVoting { get; set; }
        public TurnTimerState? TurnTimerState { get; set; }
        public string? FirstPlayerId { get; set; }
        public bool ThuneeOpportunitiesComplete { get; set; }
        public bool WinningKhanakCall { get; set; }
        public List<BallHistory> GameHistory { get; set; } = new();
        public BiddingState? BiddingState { get; set; }
        public Dictionary<string, int>? TargetScores { get; set; }

        // Special calls tracking
        public List<string> ThuneePassedPlayers { get; set; } = new();
        public string? DoubleCallerId { get; set; }
        public string? KhanakCallerId { get; set; }

        public List<Card>? CurrentHandCards { get; set; } = new();
        public Dictionary<string, int> HandScores { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class BiddingState
    {
        public int CurrentBid { get; set; }
        public string? CurrentBidder { get; set; }
        public string? HighestBidder { get; set; }
        public bool BiddingComplete { get; set; }
        public Dictionary<int, int> TeamBids { get; set; } = new();
        public Dictionary<int, string> TeamBidders { get; set; } = new();
        public List<string> BiddingOrder { get; set; } = new();
        public int CurrentBidderIndex { get; set; }
        public HashSet<string> PassedPlayers { get; set; } = new();
        public HashSet<int> TeamsAllowedToBid { get; set; } = new();
        public HashSet<int> TeamsThatBid { get; set; } = new();
    }



    public class GameState
    {
        public string Phase { get; set; } = "waiting";
        public string? CurrentPlayer { get; set; }
        public int CurrentBall { get; set; } = 1;
        public int CurrentHand { get; set; } = 1;
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    public class TimeSettings
    {
        public List<int> PlayTimeframeOptions { get; set; } = new() { 3, 4, 5, 6, 60 };
        public int VotingTimeLimit { get; set; } = 15;
        public ThuneeCallingDurations ThuneeCallingDurations { get; set; } = new();
        public int TrumpDisplayDuration { get; set; } = 10;
        public int CardDealingSpeed { get; set; } = 300;
        public int TimerUpdateInterval { get; set; } = 100;
    }

    public class ThuneeCallingDurations
    {
        public int Trumper { get; set; } = 5;
        public int FirstRemaining { get; set; } = 3;
        public int LastRemaining { get; set; } = 2;
    }

    public class Hand
    {
        public int Id { get; set; }
        public List<Card> Cards { get; set; } = new();
        public string? WinnerId { get; set; }
        public string? WinnerName { get; set; }
        public int WinnerTeam { get; set; }
        public int Points { get; set; }
        public List<CardPlay> Plays { get; set; } = new();
    }

    public class CardPlay
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public Card Card { get; set; } = new();
        public DateTime PlayedAt { get; set; }
    }

    public class PlayedCard
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public Card Card { get; set; } = new();
        public int PlayOrder { get; set; }
        public DateTime PlayedAt { get; set; } = DateTime.UtcNow;
    }

    public class JordhiCall
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int Value { get; set; }
        public string JordhiSuit { get; set; } = string.Empty;
        public bool IsValidCards { get; set; }
        public bool IsValidHandCount { get; set; }
        public bool IsFullyValid { get; set; }
        public int HandNumber { get; set; }
        public List<Card> JordhiCards { get; set; } = new();
        public bool CardsRevealed { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class KhanakCall
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int OpposingTeam { get; set; }
        public int Threshold { get; set; }
        public int OpposingTeamPoints { get; set; }
        public List<Hand> OpposingTeamHands { get; set; } = new();
        public int TeamJordhiPoints { get; set; }
        public int OpposingTeamJordhiPoints { get; set; }
        public int HandNumber { get; set; }
        public bool IsValid { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class DoubleCall
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int HandNumber { get; set; }
        public bool IsValid { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class ThuneeCall
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public string TrumpSuit { get; set; } = string.Empty;
        public Card FirstCard { get; set; } = new();
        public DateTime Timestamp { get; set; }
    }

    public class FourBallCall
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public string AccusedPlayerId { get; set; } = string.Empty;
        public string AccusedPlayerName { get; set; } = string.Empty;
        public int AccusedTeam { get; set; }
        public string Option { get; set; } = string.Empty; // "Never follow suit", "Under chopped"
        public int HandNumber { get; set; }
        public bool IsValid { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class TimeframeVoting
    {
        public Dictionary<string, int> Votes { get; set; } = new();
        public Dictionary<int, int> VoteCounts { get; set; } = new();
        public bool IsComplete { get; set; }
        public int? SelectedTimeframe { get; set; }
        public DateTime StartTime { get; set; }
        public int TimeLimit { get; set; }
    }

    public class TurnTimerState
    {
        public string? CurrentPlayerId { get; set; }
        public int TimeRemaining { get; set; }
        public bool TimerActive { get; set; }
        public DateTime StartTime { get; set; }
        public int InitialTime { get; set; }
    }

    public class BallHistory
    {
        public int BallId { get; set; }
        public int Winner { get; set; }
        public Dictionary<string, int> Points { get; set; } = new();
        public string? NextDealer { get; set; }
        public Dictionary<string, int> BallScores { get; set; } = new();
        public bool FourBallAwarded { get; set; }
        public string? FourBallOption { get; set; }
        public int? FourBallWinningTeam { get; set; }
        public int BallsAwarded { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class GameLobby
    {
        public string GameLobbyCode { get; set; } = string.Empty;
        public string LobbyCode { get; set; } = string.Empty;
        public string Lobby1Code { get; set; } = string.Empty;
        public string Lobby2Code { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
        public List<Player> Players { get; set; } = new();
        public List<string> OriginalLobbies { get; set; } = new();
        public Dictionary<int, List<Player>> Teams { get; set; } = new();
        public Dictionary<int, string> TeamNames { get; set; } = new();
        public bool GameStarted { get; set; }
        public GameState GameState { get; set; } = new();
        public string? DealerId { get; set; }
        public string? TrumpSelectorId { get; set; }
        public string? TrumpSuit { get; set; }
        public Dictionary<string, List<Card>> PlayerCards { get; set; } = new();
        public List<Spectator> Spectators { get; set; } = new();
    }

    public class Spectator
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public DateTime JoinedAt { get; set; }
    }

    public class VideoRoom
    {
        public string RoomId { get; set; } = string.Empty;
        public HashSet<string> Participants { get; set; } = new();
    }
}
