using ThuneeAPI.Models;
using ThuneeAPI.Utils;

namespace ThuneeAPI.Services
{
    public class GameService : IGameService
    {
        private readonly ILobbyService _lobbyService;
        private readonly ICardService _cardService;
        private readonly IBallService _ballService;
        private readonly ITurnService _turnService;
        private readonly ITimeframeService _timeframeService;

        public GameService(
            ILobbyService lobbyService,
            ICardService cardService,
            IBallService ballService,
            ITurnService turnService,
            ITimeframeService timeframeService)
        {
            _lobbyService = lobbyService;
            _cardService = cardService;
            _ballService = ballService;
            _turnService = turnService;
            _timeframeService = timeframeService;
        }

        // Events
        public event Func<string, TimeframeOptionsResponse, Task>? TimeframeOptionsUpdated;
        public event Func<string, TimeframeVoteResponse, Task>? TimeframeVoteUpdated;
        public event Func<string, CardDealtResponse, Task>? CardDealt;
        public event Func<string, DealerFoundResponse, Task>? DealerFound;
        public event Func<string, CardsDealtResponse, Task>? CardsDealt;
        public event Func<string, TrumpSelectedResponse, Task>? TrumpSelected;
        public event Func<string, PlayerTurnResponse, Task>? PlayerTurn;
        public event Func<string, CardPlayedResponse, Task>? CardPlayed;
        public event Func<string, HandCompletedResponse, Task>? HandCompleted;
        public event Func<string, BallCompletedResponse, Task>? BallCompleted;
        public event Func<string, GameEndedResponse, Task>? GameEnded;
        public event Func<string, JordhiCalledResponse, Task>? JordhiCalled;
        public event Func<string, JordhiCardsRevealedResponse, Task>? JordhiCardsRevealed;
        public event Func<string, FourBallResultResponse, Task>? FourBallResult;
        public event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        public event Func<string, Task>? DealerDeterminationReset;
        public event Func<string, DealerDeterminationStartedResponse, Task>? DealerDeterminationStarted;
        public event Func<string, DealingCardToResponse, Task>? DealingCardTo;
        public event Func<string, DealerDeterminationAbortedResponse, Task>? DealerDeterminationAborted;
        public event Func<string, FirstFourDealtResponse, Task>? FirstFourDealt;
        public event Func<string, BiddingCompletedResponse, Task>? BiddingCompleted;
        public event Func<string, ShuffleAnimationResponse, Task>? ShuffleAnimation;
        public event Func<string, ShuffleCompleteResponse, Task>? ShuffleComplete;
        public event Func<string, CutRequestedResponse, Task>? CutRequested;
        public event Func<string, CutCompleteResponse, Task>? CutComplete;

        // Bidding events
        public event Func<string, BidPlacedResponse, Task>? BidPlaced;
        public event Func<string, BidPassedResponse, Task>? BidPassed;
        public event Func<string, BiddingStateUpdatedResponse, Task>? BiddingStateUpdated;
        public event Func<string, YourTurnToBidResponse, Task>? YourTurnToBid;

        public async Task<bool> VoteTimeframeAsync(string connectionId, int timeframe)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            var success = _timeframeService.RecordTimeframeVote(lobby, connectionId, timeframe);
            if (!success) return false;

            // Update vote results
            _timeframeService.UpdateVoteResults(lobby);

            // Check if all players have voted
            if (_timeframeService.AllPlayersVoted(lobby))
            {
                var selectedTimeframe = _timeframeService.DetermineSelectedTimeframe(lobby);
                if (selectedTimeframe.HasValue)
                {
                    // Store the selected timeframe
                    if (lobby.TimeframeVoting != null)
                    {
                        lobby.TimeframeVoting.SelectedTimeframe = selectedTimeframe.Value;
                        lobby.TimeframeVoting.IsComplete = true;
                    }

                    // Trigger event to notify all players
                    if (TimeframeVoteUpdated != null)
                    {
                        await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                        {
                            Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                            VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                            AllPlayersVoted = true,
                            SelectedTimeframe = selectedTimeframe.Value
                        });
                    }

                    // After a delay, transition to dealer determination phase
                    _ = Task.Delay(3000).ContinueWith(async _ =>
                    {
                        if (GamePhaseUpdated != null)
                        {
                            await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                            {
                                Phase = "dealer-determination",
                                Players = lobby.Players
                            });
                        }

                        // DO NOT automatically start dealer determination here
                        // Let the client-side trigger it to match Node.js implementation
                    });
                }
            }
            else
            {
                // Trigger event to update vote counts
                if (TimeframeVoteUpdated != null)
                {
                    await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                    {
                        Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                        VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                        AllPlayersVoted = false,
                        SelectedTimeframe = null
                    });
                }
            }

            return true;
        }

        public async Task<bool> RequestTimeframeOptionsAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            var votingTimeLimit = lobby.TimeSettings?.VotingTimeLimit ?? 15;

            if (TimeframeOptionsUpdated != null)
            {
                await TimeframeOptionsUpdated(lobby.LobbyCode, new TimeframeOptionsResponse
                {
                    TimeOptions = timeOptions,
                    VotingTimeLimit = votingTimeLimit
                });
            }

            return true;
        }

        public async Task<bool> PlayCardAsync(string connectionId, Card card)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that it's the player's turn
            if (lobby.TurnTimerState?.CurrentPlayerId != connectionId)
            {
                return false;
            }

            // Find and remove the card from player's hand
            var playerCard = _cardService.FindCardInPlayerHand(connectionId, card, lobby.PlayerCards);
            if (playerCard == null) return false;

            var success = _cardService.RemoveCardFromPlayerHand(connectionId, card, lobby.PlayerCards);
            if (!success) return false;

            // Add card to current hand
            if (lobby.CurrentHandCards == null)
            {
                lobby.CurrentHandCards = new List<Card>();
            }

            playerCard.PlayedBy = connectionId;
            lobby.CurrentHandCards.Add(playerCard);

            // Trigger card played event
            if (CardPlayed != null)
            {
                await CardPlayed(lobby.LobbyCode, new CardPlayedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Card = playerCard,
                    HandId = lobby.CurrentHandId,
                    HandCards = lobby.CurrentHandCards
                });
            }

            // Check if hand is complete (4 cards played)
            if (lobby.CurrentHandCards.Count == 4)
            {
                await ProcessHandCompletionAsync(lobby);
            }
            else
            {
                // Set next player's turn
                await SetNextPlayerTurnAsync(lobby);
            }

            return true;
        }

        public async Task<bool> SelectTrumpAsync(string connectionId, string trumpSuit)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that this player can select trump
            if (lobby.TrumpSelectorId != connectionId)
            {
                return false;
            }

            lobby.TrumpSuit = trumpSuit;

            // Trigger trump selected event
            if (TrumpSelected != null)
            {
                await TrumpSelected(lobby.LobbyCode, new TrumpSelectedResponse
                {
                    TrumpSuit = trumpSuit,
                    TrumpSelectorId = connectionId,
                    TrumpSelectorName = player.Name
                });
            }

            // After trump selection, transition to waiting for final cards phase
            _ = Task.Delay(2000).ContinueWith(async _ =>
            {
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "waiting-for-final-cards",
                        Players = lobby.Players
                    });
                }
            });

            return true;
        }

        public async Task<bool> BidAsync(string connectionId, int bidAmount)
        {
            Console.WriteLine($"[BidAsync] Bid received from {connectionId}: {bidAmount}");

            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null)
            {
                Console.WriteLine($"[BidAsync] Lobby not found for connectionId: {connectionId}");
                return false;
            }

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null)
            {
                Console.WriteLine($"[BidAsync] Player not found for connectionId: {connectionId}");
                return false;
            }

            // Initialize bidding state if not exists
            if (lobby.BiddingState == null)
            {
                Console.WriteLine($"[BidAsync] Initializing bidding state");
                await InitializeBiddingState(lobby);
            }

            // Validate bid amount
            if (bidAmount <= lobby.BiddingState.CurrentBid || bidAmount < 10 || bidAmount > 104)
            {
                Console.WriteLine($"[BidAsync] Invalid bid amount: {bidAmount}, current bid: {lobby.BiddingState.CurrentBid}");
                return false;
            }

            // Check valid bid values (10, 20, 30, ..., 100, 104)
            var validBids = new[] { 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 104 };
            if (!validBids.Contains(bidAmount))
            {
                Console.WriteLine($"[BidAsync] Bid {bidAmount} is not a valid bid value");
                return false;
            }

            // Check if it's this player's turn to bid
            var currentBidder = lobby.BiddingState.BiddingOrder[lobby.BiddingState.CurrentBidderIndex];
            if (connectionId != currentBidder)
            {
                Console.WriteLine($"[BidAsync] Not player {connectionId}'s turn to bid. Current bidder is {currentBidder}");
                return false;
            }

            // Check team-based bidding rules like Node.js server
            var playerTeam = player.Team;

            // Check if this team is allowed to bid
            if (!lobby.BiddingState.TeamsAllowedToBid.Contains(playerTeam))
            {
                Console.WriteLine($"[BidAsync] Team {playerTeam} is not allowed to bid yet");
                return false;
            }

            // Check if this team has already bid (only one player per team can bid)
            if (lobby.BiddingState.TeamsThatBid.Contains(playerTeam))
            {
                Console.WriteLine($"[BidAsync] Team {playerTeam} has already bid");
                return false;
            }

            // Get dealer team for bid validation
            var dealerTeam = lobby.Players.FirstOrDefault(p => p.IsDealer)?.Team ?? 1;
            var isOpposingTeam = player.Team != dealerTeam;

            // Check bid limits based on team
            if (!isOpposingTeam && bidAmount > 100)
            {
                Console.WriteLine($"[BidAsync] Bid {bidAmount} exceeds maximum of 100 for the dealer's team");
                return false;
            }

            if (isOpposingTeam && bidAmount > 104)
            {
                Console.WriteLine($"[BidAsync] Bid {bidAmount} exceeds maximum of 104 for the opposing team");
                return false;
            }

            // Stop the current bidding timer
            StopBiddingTimer(lobby);

            // Record the bid
            lobby.BiddingState.CurrentBid = bidAmount;
            lobby.BiddingState.CurrentBidder = connectionId;
            lobby.BiddingState.HighestBidder = connectionId;
            lobby.BiddingState.TeamBids[player.Team] = bidAmount;
            lobby.BiddingState.TeamBidders[player.Team] = connectionId;
            lobby.BiddingState.TeamsThatBid.Add(player.Team);

            // CRITICAL: Allow opposing team to bid after dealer's team bids (like Node.js server)
            var opposingTeam = dealerTeam == 1 ? 2 : 1;

            if (player.Team == dealerTeam && !lobby.BiddingState.TeamsAllowedToBid.Contains(opposingTeam))
            {
                lobby.BiddingState.TeamsAllowedToBid.Add(opposingTeam);
                Console.WriteLine($"[BidAsync] Dealer's team bid, now allowing opposing team {opposingTeam} to bid");
            }

            // Set trump selector
            lobby.TrumpSelectorId = connectionId;

            Console.WriteLine($"[BidAsync] Bid placed successfully: {player.Name} (Team {player.Team}) bid {bidAmount}");

            // Broadcast bid to all players
            if (BidPlaced != null)
            {
                await BidPlaced(lobby.LobbyCode, new BidPlacedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Bid = bidAmount
                });
            }

            // Move to next bidder
            await MoveToNextBidder(lobby);

            return true;
        }

        public async Task<bool> PassBidAsync(string connectionId)
        {
            Console.WriteLine($"[PassBidAsync] Pass bid received from {connectionId}");

            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null)
            {
                Console.WriteLine($"[PassBidAsync] Lobby not found for connectionId: {connectionId}");
                return false;
            }

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null)
            {
                Console.WriteLine($"[PassBidAsync] Player not found for connectionId: {connectionId}");
                return false;
            }

            // Initialize bidding state if not exists
            if (lobby.BiddingState == null)
            {
                Console.WriteLine($"[PassBidAsync] Initializing bidding state");
                await InitializeBiddingState(lobby);
            }

            // Check if it's this player's turn to bid
            var currentBidder = lobby.BiddingState.BiddingOrder[lobby.BiddingState.CurrentBidderIndex];
            if (connectionId != currentBidder)
            {
                Console.WriteLine($"[PassBidAsync] Not player {connectionId}'s turn to bid. Current bidder is {currentBidder}");
                return false;
            }

            // Stop the current bidding timer
            StopBiddingTimer(lobby);

            // Add player to passed players
            lobby.BiddingState.PassedPlayers.Add(connectionId);

            Console.WriteLine($"[PassBidAsync] Player {player.Name} passed. Passed players: {lobby.BiddingState.PassedPlayers.Count}");

            // Broadcast pass to all players
            if (BidPassed != null)
            {
                await BidPassed(lobby.LobbyCode, new BidPassedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    AutoPass = false,
                    Reason = null
                });
            }

            // Move to next bidder
            await MoveToNextBidder(lobby);

            return true;
        }

        public async Task<bool> ShuffleCardsAsync(string connectionId, string shuffleType)
        {
            Console.WriteLine($"[ShuffleCardsAsync] Called with connectionId: {connectionId}, shuffleType: {shuffleType}");

            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null)
            {
                Console.WriteLine($"[ShuffleCardsAsync] Lobby not found for connectionId: {connectionId}");
                return false;
            }

            Console.WriteLine($"[ShuffleCardsAsync] Found lobby: {lobby.LobbyCode}, DealerId: '{lobby.DealerId}' (length: {lobby.DealerId?.Length ?? 0})");
            Console.WriteLine($"[ShuffleCardsAsync] Lobby object hash: {lobby.GetHashCode()}");
            Console.WriteLine($"[ShuffleCardsAsync] Lobby GameStarted: {lobby.GameStarted}, DealerFound: {lobby.DealerFound}");

            // Validate that this player can shuffle (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                Console.WriteLine($"[ShuffleCardsAsync] Player {connectionId} is not the dealer ({lobby.DealerId}), cannot shuffle");
                return false;
            }

            Console.WriteLine($"[ShuffleCardsAsync] Player {connectionId} is the dealer, proceeding with shuffle");

            // Create and shuffle a new deck
            var deck = _cardService.CreateGameDeck();
            lobby.GameDeck = _cardService.ShuffleDeck(deck);

            Console.WriteLine($"[ShuffleCardsAsync] Deck shuffled, triggering ShuffleAnimation event");

            // Trigger shuffle animation event
            if (ShuffleAnimation != null)
            {
                Console.WriteLine($"[ShuffleCardsAsync] Sending ShuffleAnimation event to lobby {lobby.LobbyCode}");
                await ShuffleAnimation(lobby.LobbyCode, new ShuffleAnimationResponse
                {
                    ShuffleType = shuffleType,
                    DealerId = connectionId
                });
                Console.WriteLine($"[ShuffleCardsAsync] ShuffleAnimation event sent successfully");
            }
            else
            {
                Console.WriteLine($"[ShuffleCardsAsync] WARNING: ShuffleAnimation event is null!");
            }

            // After animation delay, trigger shuffle complete and move to cut phase
            _ = Task.Delay(3000).ContinueWith(async _ =>
            {
                Console.WriteLine($"[ShuffleCardsAsync] 3 second delay completed, triggering ShuffleComplete event");

                if (ShuffleComplete != null)
                {
                    Console.WriteLine($"[ShuffleCardsAsync] Sending ShuffleComplete event to lobby {lobby.LobbyCode}");
                    await ShuffleComplete(lobby.LobbyCode, new ShuffleCompleteResponse
                    {
                        DealerId = connectionId
                    });
                    Console.WriteLine($"[ShuffleCardsAsync] ShuffleComplete event sent successfully");
                }
                else
                {
                    Console.WriteLine($"[ShuffleCardsAsync] WARNING: ShuffleComplete event is null!");
                }

                // Transition to cut phase
                if (GamePhaseUpdated != null)
                {
                    Console.WriteLine($"[ShuffleCardsAsync] Sending GamePhaseUpdated event to cut phase for lobby {lobby.LobbyCode}");
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "cut",
                        Players = lobby.Players
                    });
                    Console.WriteLine($"[ShuffleCardsAsync] GamePhaseUpdated event sent successfully");
                }
                else
                {
                    Console.WriteLine($"[ShuffleCardsAsync] WARNING: GamePhaseUpdated event is null!");
                }

                // Request cut from the player to the left of dealer using position-based logic
                var dealer = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
                if (dealer != null && dealer.Position > 0)
                {
                    // Get the position to the left of the dealer
                    var cutterPosition = PlayerPositionUtils.GetPositionToLeft(dealer.Position);
                    var cutter = lobby.Players.FirstOrDefault(p => p.Position == cutterPosition);

                    if (cutter != null && CutRequested != null)
                    {
                        await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                        {
                            PlayerId = cutter.Id,
                            PlayerName = cutter.Name
                        });
                    }
                }
                else
                {
                    // Fallback to index-based approach if positions are not set
                    var dealerIndex = lobby.Players.FindIndex(p => p.Id == connectionId);
                    var cutterIndex = (dealerIndex + 1) % lobby.Players.Count;
                    var cutter = lobby.Players[cutterIndex];

                    if (CutRequested != null)
                    {
                        await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                        {
                            PlayerId = cutter.Id,
                            PlayerName = cutter.Name
                        });
                    }
                }
            });

            Console.WriteLine($"[ShuffleCardsAsync] Shuffle completed successfully, returning true");
            return true;
        }

        public async Task<bool> RequestCutAsync(string connectionId, string playerId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Validate that this player can request a cut (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return false;
            }

            // Find the player who should cut using position-based logic
            var dealer = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (dealer == null) return false;

            Player? cutter = null;

            // If a specific player ID is provided, use that player
            if (!string.IsNullOrEmpty(playerId))
            {
                cutter = lobby.Players.FirstOrDefault(p => p.Id == playerId);
            }
            else if (dealer.Position > 0)
            {
                // Use position-based logic to find the player to the left of dealer
                var cutterPosition = PlayerPositionUtils.GetPositionToLeft(dealer.Position);
                cutter = lobby.Players.FirstOrDefault(p => p.Position == cutterPosition);
            }
            else
            {
                // Fallback to index-based approach if positions are not set
                var dealerIndex = lobby.Players.FindIndex(p => p.Id == connectionId);
                var cutterIndex = (dealerIndex + 1) % lobby.Players.Count;
                cutter = lobby.Players[cutterIndex];
            }

            if (cutter == null) return false;

            // Trigger cut requested event
            if (CutRequested != null)
            {
                await CutRequested(lobby.LobbyCode, new CutRequestedResponse
                {
                    PlayerId = cutter.Id,
                    PlayerName = cutter.Name
                });
            }

            return true;
        }

        public async Task<bool> CutCardsAsync(string connectionId, int cutPosition)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                return false;
            }

            // Cut the deck
            lobby.GameDeck = _cardService.CutDeck(lobby.GameDeck, cutPosition);

            // Trigger cut complete event
            if (CutComplete != null)
            {
                await CutComplete(lobby.LobbyCode, new CutCompleteResponse
                {
                    Cut = true,
                    Position = cutPosition.ToString(),
                    PlayerId = connectionId,
                    PlayerName = player.Name
                });
            }

            // After animation delay, move to deal first four phase
            _ = Task.Delay(1500).ContinueWith(async _ =>
            {
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "deal-first-four",
                        Players = lobby.Players
                    });
                }
            });

            return true;
        }

        public async Task<bool> SkipCutAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Trigger cut complete event with no cut
            if (CutComplete != null)
            {
                await CutComplete(lobby.LobbyCode, new CutCompleteResponse
                {
                    Cut = false,
                    Position = null,
                    PlayerId = connectionId,
                    PlayerName = player.Name
                });
            }

            // Move to deal first four phase immediately
            if (GamePhaseUpdated != null)
            {
                await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                {
                    Phase = "deal-first-four",
                    Players = lobby.Players
                });
            }

            return true;
        }

        public async Task<bool> DealCardsAsync(string connectionId, int cardsPerPlayer)
        {
            Console.WriteLine($"[DealCardsAsync] Called with connectionId: {connectionId}, cardsPerPlayer: {cardsPerPlayer}");

            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null)
            {
                Console.WriteLine($"[DealCardsAsync] Lobby not found for connectionId: {connectionId}");
                return false;
            }

            Console.WriteLine($"[DealCardsAsync] Found lobby: {lobby.LobbyCode}, DealerId: '{lobby.DealerId}' (length: {lobby.DealerId?.Length ?? 0})");

            // Validate that this player can deal (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                Console.WriteLine($"[DealCardsAsync] Player {connectionId} is not the dealer ({lobby.DealerId}), cannot deal cards");
                return false;
            }

            Console.WriteLine($"[DealCardsAsync] Player {connectionId} is the dealer, proceeding with dealing {cardsPerPlayer} cards");

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                Console.WriteLine($"[DealCardsAsync] No deck found, creating new deck");
                // Create a new deck if none exists
                lobby.GameDeck = _cardService.CreateGameDeck();
                Console.WriteLine($"[DealCardsAsync] Created new deck with {lobby.GameDeck.Count} cards");
            }
            else
            {
                Console.WriteLine($"[DealCardsAsync] Using existing deck with {lobby.GameDeck.Count} cards");
            }

            Console.WriteLine($"[DealCardsAsync] About to call DealCardsWithAnimationAsync");
            // Deal cards with individual card events for animation
            await DealCardsWithAnimationAsync(lobby, cardsPerPlayer);
            Console.WriteLine($"[DealCardsAsync] DealCardsWithAnimationAsync completed successfully");

            return true;
        }

        private async Task DealCardsWithAnimationAsync(Lobby lobby, int cardsPerPlayer)
        {
            Console.WriteLine($"[DealCardsWithAnimationAsync] Starting to deal {cardsPerPlayer} cards to {lobby.Players.Count} players in lobby {lobby.LobbyCode}");

            var deckCopy = new List<Card>(lobby.GameDeck);
            var allDealtCards = new HashSet<string>();

            Console.WriteLine($"[DealCardsWithAnimationAsync] Deck has {deckCopy.Count} cards available");

            // Initialize player cards if not already done
            if (lobby.PlayerCards == null)
            {
                Console.WriteLine($"[DealCardsWithAnimationAsync] Initializing PlayerCards dictionary");
                lobby.PlayerCards = new Dictionary<string, List<Card>>();
            }

            foreach (var player in lobby.Players)
            {
                if (!lobby.PlayerCards.ContainsKey(player.Id))
                {
                    Console.WriteLine($"[DealCardsWithAnimationAsync] Initializing cards for player {player.Name} ({player.Id})");
                    lobby.PlayerCards[player.Id] = new List<Card>();
                }
                else
                {
                    Console.WriteLine($"[DealCardsWithAnimationAsync] Player {player.Name} ({player.Id}) already has {lobby.PlayerCards[player.Id].Count} cards");
                }
            }

            // Deal cards in rounds (one card to each player at a time) with animation
            // Use position-based dealing order: dealer (position 3) -> right (position 4) -> partner (position 1) -> left (position 2)
            var dealingOrder = new List<int> { 3, 4, 1, 2 }; // Positions in dealing order
            Console.WriteLine($"[DealCardsWithAnimationAsync] Using dealing order: {string.Join(" -> ", dealingOrder)}");

            for (int round = 0; round < cardsPerPlayer; round++)
            {
                Console.WriteLine($"[DealCardsWithAnimationAsync] Starting round {round + 1} of {cardsPerPlayer}");

                foreach (var position in dealingOrder)
                {
                    var player = lobby.Players.FirstOrDefault(p => p.Position == position);
                    if (player == null)
                    {
                        Console.WriteLine($"[DealCardsWithAnimationAsync] WARNING: No player found at position {position}");
                        continue;
                    }
                    Console.WriteLine($"[DealCardsWithAnimationAsync] Dealing card to player {player.Name} ({player.Id}), deck has {deckCopy.Count} cards");

                    if (deckCopy.Count > 0)
                    {
                        Card? card = null;
                        string cardKey;

                        // Find a unique card that hasn't been dealt yet
                        do
                        {
                            if (deckCopy.Count == 0) break;

                            card = deckCopy[0];
                            deckCopy.RemoveAt(0);
                            cardKey = $"{card.Value}_{card.Suit}";

                            // If this card has already been dealt, try another
                            if (allDealtCards.Contains(cardKey))
                            {
                                Console.WriteLine($"Card {card.Value} of {card.Suit} already dealt, trying another");
                                card = null;
                            }
                        } while (card == null && deckCopy.Count > 0);

                        if (card != null)
                        {
                            lobby.PlayerCards[player.Id].Add(card);
                            allDealtCards.Add($"{card.Value}_{card.Suit}");

                            Console.WriteLine($"[DealCardsWithAnimationAsync] Dealing {card.Value} of {card.Suit} to {player.Name}, triggering CardDealt event");

                            // Emit individual card dealt event for animation
                            if (CardDealt != null)
                            {
                                await CardDealt(lobby.LobbyCode, new CardDealtResponse
                                {
                                    PlayerId = player.Id,
                                    Card = card,
                                    IsDealer = player.IsDealer
                                });
                                Console.WriteLine($"[DealCardsWithAnimationAsync] CardDealt event sent for {card.Value} of {card.Suit} to {player.Name}");
                            }
                            else
                            {
                                Console.WriteLine($"[DealCardsWithAnimationAsync] WARNING: CardDealt event is null!");
                            }

                            // Small delay between each card for animation
                            await Task.Delay(200);
                        }
                        else
                        {
                            Console.WriteLine($"[DealCardsWithAnimationAsync] No card available for player {player.Name}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[DealCardsWithAnimationAsync] Deck is empty, cannot deal to player {player.Name}");
                    }
                }
            }

            // After all cards are dealt, trigger completion event
            if (CardsDealt != null)
            {
                await CardsDealt(lobby.LobbyCode, new CardsDealtResponse
                {
                    PlayerCards = lobby.PlayerCards,
                    CardsPerPlayer = cardsPerPlayer,
                    DealingComplete = true
                });
            }

            // Update the lobby's deck to remove dealt cards
            lobby.GameDeck = deckCopy;

            // Trigger appropriate phase transitions based on cards dealt
            if (cardsPerPlayer == 4)
            {
                // After dealing first 4 cards, wait and then trigger bidding phase
                _ = Task.Delay(5000).ContinueWith(async _ =>
                {
                    try
                    {
                        Console.WriteLine($"[DealCardsAsync] Starting bidding phase initialization for lobby {lobby.LobbyCode}");

                        if (FirstFourDealt != null)
                        {
                            await FirstFourDealt(lobby.LobbyCode, new FirstFourDealtResponse());
                        }

                        if (GamePhaseUpdated != null)
                        {
                            await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                            {
                                Phase = "bidding",
                                Players = lobby.Players
                            });
                        }

                        // Initialize bidding state and start bidding
                        Console.WriteLine($"[DealCardsAsync] Calling InitializeBiddingState for lobby {lobby.LobbyCode}");
                        await InitializeBiddingState(lobby);
                        Console.WriteLine($"[DealCardsAsync] InitializeBiddingState completed for lobby {lobby.LobbyCode}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[DealCardsAsync] ERROR in bidding initialization: {ex.Message}");
                        Console.WriteLine($"[DealCardsAsync] Stack trace: {ex.StackTrace}");
                    }
                });
            }
            else if (cardsPerPlayer == 2)
            {
                // After dealing final 2 cards, wait and then start gameplay
                _ = Task.Delay(3000).ContinueWith(async _ =>
                {
                    // Determine first player (to the right of trump selector)
                    var trumpSelector = lobby.Players.FirstOrDefault(p => p.IsTrumpSelector);
                    if (trumpSelector != null)
                    {
                        var trumpSelectorIndex = lobby.Players.FindIndex(p => p.Id == trumpSelector.Id);
                        var firstPlayerIndex = (trumpSelectorIndex + 1) % lobby.Players.Count;
                        var firstPlayer = lobby.Players[firstPlayerIndex];
                        lobby.FirstPlayerId = firstPlayer.Id;

                        // Start the first player's turn
                        await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, firstPlayer.Id);
                    }
                });
            }
        }

        public async Task<bool> CallJordhiAsync(string connectionId, int value, string jordhiSuit, List<Card> jordhiCards)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Jordhi call
            var isValidCards = ValidateJordhiCards(jordhiCards, jordhiSuit, value, lobby.TrumpSuit);
            var handsWonByTeam = CountHandsWonByTeam(lobby, player.Team);
            var isValidHandCount = handsWonByTeam > 0; // Team must have won at least one hand
            var isFullyValid = isValidCards && isValidHandCount;

            // Create Jordhi call record
            var jordhiCall = new JordhiCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                Value = value,
                HandNumber = handsWonByTeam,
                IsValidCards = isValidCards,
                IsValidHandCount = isValidHandCount,
                IsFullyValid = isFullyValid,
                JordhiSuit = jordhiSuit,
                JordhiCards = isValidCards ? jordhiCards : new List<Card>()
            };

            lobby.JordhiCalls.Add(jordhiCall);

            // Adjust target scores if valid
            if (isFullyValid)
            {
                AdjustTargetScoresForJordhi(lobby, player.Team, value);
            }

            // Trigger Jordhi called event
            if (JordhiCalled != null)
            {
                await JordhiCalled(lobby.LobbyCode, new JordhiCalledResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Value = value,
                    HandNumber = handsWonByTeam,
                    TargetScores = lobby.TargetScores ?? new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } },
                    IsValidCards = isValidCards,
                    IsValidHandCount = isValidHandCount,
                    IsFullyValid = isFullyValid,
                    JordhiSuit = jordhiSuit,
                    JordhiCards = isValidCards ? jordhiCards : new List<Card>()
                });
            }

            return true;
        }

        public async Task<bool> RevealJordhiAsync(string connectionId, int jordhiValue, string jordhiSuit, List<Card> jordhiCards, bool revealCards)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Find the Jordhi call to reveal
            var jordhiCall = lobby.JordhiCalls.LastOrDefault(j => j.PlayerId == connectionId && j.Value == jordhiValue);
            if (jordhiCall == null) return false;

            // Only allow revealing valid Jordhi calls
            if (!jordhiCall.IsFullyValid) return false;

            // Trigger Jordhi cards revealed event
            if (JordhiCardsRevealed != null)
            {
                await JordhiCardsRevealed(lobby.LobbyCode, new JordhiCardsRevealedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    JordhiValue = jordhiValue,
                    JordhiSuit = jordhiSuit,
                    JordhiCards = jordhiCards,
                    RevealCards = revealCards
                });
            }

            return true;
        }

        public async Task<bool> CallDoubleAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Double call - only valid in last hand if opposite team hasn't taken any hands
            if (lobby.CurrentHandId != 6) return false; // Must be last hand

            var oppositeTeam = player.Team == 1 ? 2 : 1;
            var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);
            if (oppositeTeamHands > 0) return false; // Opposite team must not have won any hands

            // Create Double call record
            var doubleCall = new DoubleCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                HandNumber = lobby.CurrentHandId,
                IsValid = true
            };

            lobby.DoubleCalls.Add(doubleCall);

            // Mark that Double was called for ball completion processing
            lobby.DoubleCallerId = connectionId;

            return true;
        }

        public async Task<bool> CallKhanakAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate Khanak call - only valid in last trick if calling team has made at least one valid Jordhi call
            // and opposing team has won at least one trick
            if (lobby.CurrentHandId != 6) return false; // Must be last hand

            var teamJordhiPoints = lobby.JordhiCalls.Where(j => j.PlayerTeam == player.Team && j.IsFullyValid).Sum(j => j.Value);
            if (teamJordhiPoints == 0) return false; // Team must have made at least one valid Jordhi call

            var oppositeTeam = player.Team == 1 ? 2 : 1;
            var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);
            if (oppositeTeamHands == 0) return false; // Opposing team must have won at least one trick

            var oppositeTeamJordhiPoints = lobby.JordhiCalls.Where(j => j.PlayerTeam == oppositeTeam && j.IsFullyValid).Sum(j => j.Value);
            var threshold = teamJordhiPoints + 10 - oppositeTeamJordhiPoints;

            // Create Khanak call record
            var khanakCall = new KhanakCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                HandNumber = lobby.CurrentHandId,
                Threshold = threshold,
                IsValid = true
            };

            lobby.KhanakCalls.Add(khanakCall);

            // Mark that Khanak was called for ball completion processing
            lobby.KhanakCallerId = connectionId;

            return true;
        }

        public async Task<bool> CallThuneeAsync(string connectionId, Card firstCard)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that player has the card they want to play
            if (!lobby.PlayerCards.ContainsKey(connectionId) ||
                !lobby.PlayerCards[connectionId].Any(c => c.Suit == firstCard.Suit && c.Value == firstCard.Value))
            {
                return false;
            }

            // Set trump suit to the suit of the first card
            lobby.TrumpSuit = firstCard.Suit;

            // Create Thunee call record
            var thuneeCall = new ThuneeCall
            {
                PlayerId = connectionId,
                PlayerName = player.Name,
                PlayerTeam = player.Team,
                FirstCard = firstCard,
                TrumpSuit = firstCard.Suit
            };

            lobby.ThuneeCalls.Add(thuneeCall);

            // Mark Thunee opportunities as complete
            lobby.ThuneeOpportunitiesComplete = true;

            // Set this player as the first to play
            lobby.FirstPlayerId = connectionId;

            // Start the player's turn with timer
            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, connectionId);

            return true;
        }

        public async Task<bool> FourBallAsync(string connectionId, string ballType, string option, string accusedPlayerId, int handNumber)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var accuser = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            var accused = lobby.Players.FirstOrDefault(p => p.Id == accusedPlayerId);
            if (accuser == null || accused == null) return false;

            bool isValid = false;
            int winningTeam = accused.Team; // Default to accused team winning if accusation is invalid
            string reason = "";

            // Validate the 4-ball claim based on option
            switch (option)
            {
                case "Never follow suit":
                    isValid = ValidateNeverFollowSuit(lobby, accusedPlayerId, handNumber);
                    reason = isValid ? "Player did not follow suit when they could have" : "Player followed suit correctly";
                    break;
                case "Under chopped":
                    isValid = ValidateUnderChopped(lobby, accusedPlayerId, handNumber);
                    reason = isValid ? "Player played lower trump when they had higher trump" : "Player played correctly";
                    break;
                default:
                    return false;
            }

            // Determine winning team - if accusation is valid, accuser's team wins; otherwise accused team wins
            winningTeam = isValid ? accuser.Team : accused.Team;

            // Award 4 balls to the winning team
            var teamKey = $"team{winningTeam}";
            lobby.BallScores[teamKey] = 4;

            // Reset the other team's score to 0
            var otherTeam = winningTeam == 1 ? 2 : 1;
            var otherTeamKey = $"team{otherTeam}";
            lobby.BallScores[otherTeamKey] = 0;

            // Create 4-ball call record
            var fourBallCall = new FourBallCall
            {
                PlayerId = connectionId,
                PlayerName = accuser.Name,
                PlayerTeam = accuser.Team,
                AccusedPlayerId = accusedPlayerId,
                AccusedPlayerName = accused.Name,
                AccusedTeam = accused.Team,
                Option = option,
                HandNumber = handNumber,
                IsValid = isValid
            };

            lobby.FourBallCalls.Add(fourBallCall);

            // Get the selected hand cards for display
            var selectedHand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            var selectedHandCards = selectedHand?.Cards ?? new List<Card>();

            // Trigger 4-ball result event
            if (FourBallResult != null)
            {
                await FourBallResult(lobby.LobbyCode, new FourBallResultResponse
                {
                    BallType = ballType,
                    Option = option,
                    TargetPlayer = accusedPlayerId,
                    TargetPlayerName = accused.Name,
                    TargetTeam = accused.Team,
                    AccuserId = connectionId,
                    AccuserName = accuser.Name,
                    AccuserTeam = accuser.Team,
                    HandNumber = handNumber,
                    IsValid = isValid,
                    WinningTeam = winningTeam,
                    BallsAwarded = 4,
                    BallScores = lobby.BallScores,
                    Reason = reason,
                    SelectedHandCards = selectedHandCards
                });
            }

            return true;
        }

        public Task<bool> SetDealerAsync(string connectionId, string dealerId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return Task.FromResult(false);

            lobby.DealerId = dealerId;
            lobby.DealerFound = true;

            var dealer = lobby.Players.FirstOrDefault(p => p.Id == dealerId);
            if (dealer != null)
            {
                dealer.IsDealer = true;
            }

            return Task.FromResult(true);
        }

        public async Task<bool> StartDealerDeterminationAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Get matched lobby if it exists
            Lobby? matchedLobby = null;
            if (!string.IsNullOrEmpty(lobby.MatchedLobby))
            {
                matchedLobby = _lobbyService.GetLobby(lobby.MatchedLobby);
            }

            // Check if dealer determination is already in progress or completed in either lobby
            if (lobby.DealerDeterminationInProgress ||
                (matchedLobby != null && matchedLobby.DealerDeterminationInProgress))
            {
                Console.WriteLine($"[StartDealerDeterminationAsync] Dealer determination already in progress");
                return false;
            }

            // Check if dealer is already found in either lobby
            if (lobby.DealerFound || !string.IsNullOrEmpty(lobby.DealerId) ||
                (matchedLobby != null && (matchedLobby.DealerFound || !string.IsNullOrEmpty(matchedLobby.DealerId))))
            {
                Console.WriteLine($"[StartDealerDeterminationAsync] Dealer already found: {lobby.DealerId ?? matchedLobby?.DealerId}");
                return false;
            }

            // Get all players from both lobbies
            var allPlayers = new List<Player>();

            // Add players from current lobby
            if (lobby.Teams.ContainsKey(1))
                allPlayers.AddRange(lobby.Teams[1]);
            if (lobby.Teams.ContainsKey(2))
                allPlayers.AddRange(lobby.Teams[2]);

            // Add players from matched lobby if it exists
            if (matchedLobby != null)
            {
                if (matchedLobby.Teams.ContainsKey(1))
                    allPlayers.AddRange(matchedLobby.Teams[1]);
                if (matchedLobby.Teams.ContainsKey(2))
                    allPlayers.AddRange(matchedLobby.Teams[2]);
            }

            // Remove duplicates and ensure we have exactly 4 players
            allPlayers = allPlayers.GroupBy(p => p.Id).Select(g => g.First()).ToList();
            if (allPlayers.Count != 4)
            {
                return false;
            }

            // Create a shuffled deck for dealer determination
            var dealerDeck = _cardService.CreateGameDeck();

            // Choose a random starting player index (0-3)
            var random = new Random();
            var startingPlayerIndex = random.Next(allPlayers.Count);

            // Set dealer determination state in both lobbies
            lobby.DealerDeck = dealerDeck;
            lobby.CurrentDealerPlayerIndex = startingPlayerIndex;
            lobby.DealerFound = false;
            lobby.DealerId = null;
            lobby.DealerDeterminationInProgress = true;

            // Initialize cards dealt counter for each player
            lobby.CardsDealtPerPlayer = new Dictionary<string, int>();
            foreach (var player in allPlayers)
            {
                lobby.CardsDealtPerPlayer[player.Id] = 0;
            }

            // Sync state with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.DealerDeck = new List<Card>(dealerDeck); // Copy the deck
                matchedLobby.CurrentDealerPlayerIndex = startingPlayerIndex;
                matchedLobby.DealerFound = false;
                matchedLobby.DealerId = null;
                matchedLobby.DealerDeterminationInProgress = true;
                matchedLobby.CardsDealtPerPlayer = new Dictionary<string, int>(lobby.CardsDealtPerPlayer);
            }

            // First, reset any existing dealer determination state
            if (DealerDeterminationReset != null)
            {
                await DealerDeterminationReset(lobby.LobbyCode);
                if (matchedLobby != null)
                {
                    await DealerDeterminationReset(matchedLobby.LobbyCode);
                }
            }

            // After a short delay, send the dealer determination data
            await Task.Delay(1000);

            // Send the dealer determination data to all clients
            var dealerDeterminationData = new DealerDeterminationStartedResponse
            {
                Players = allPlayers,
                CurrentPlayerIndex = startingPlayerIndex
            };

            if (DealerDeterminationStarted != null)
            {
                await DealerDeterminationStarted(lobby.LobbyCode, dealerDeterminationData);
                if (matchedLobby != null)
                {
                    await DealerDeterminationStarted(matchedLobby.LobbyCode, dealerDeterminationData);
                }
            }

            // Start the dealer determination process after another short delay
            await Task.Delay(1000);
            await DealNextCardAsync(lobby, matchedLobby, allPlayers);

            return true;
        }

        private async Task DealNextCardAsync(Lobby lobby, Lobby? matchedLobby, List<Player> allPlayers)
        {
            // If dealer already found, do nothing
            if (lobby.DealerFound)
            {
                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }
                return;
            }

            // Safety check for deck
            if (lobby.DealerDeck == null || lobby.DealerDeck.Count == 0)
            {
                // Abort dealer determination
                if (DealerDeterminationAborted != null)
                {
                    await DealerDeterminationAborted(lobby.LobbyCode, new DealerDeterminationAbortedResponse
                    {
                        Reason = "No cards left in deck"
                    });
                    if (matchedLobby != null)
                    {
                        await DealerDeterminationAborted(matchedLobby.LobbyCode, new DealerDeterminationAbortedResponse
                        {
                            Reason = "No cards left in deck"
                        });
                    }
                }
                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }
                return;
            }

            // Get the current player
            var currentPlayerIndex = lobby.CurrentDealerPlayerIndex;
            if (currentPlayerIndex >= allPlayers.Count)
            {
                lobby.DealerDeterminationInProgress = false;
                return;
            }

            var currentPlayer = allPlayers[currentPlayerIndex];

            // Check if this player already has too many cards (safety check)
            if (lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id) &&
                lobby.CardsDealtPerPlayer[currentPlayer.Id] >= 24)
            {
                // Move to the next player
                lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;
                if (matchedLobby != null)
                {
                    matchedLobby.CurrentDealerPlayerIndex = lobby.CurrentDealerPlayerIndex;
                }

                // Schedule the next card deal
                await Task.Delay(500);
                await DealNextCardAsync(lobby, matchedLobby, allPlayers);
                return;
            }

            // First, notify all clients that we're about to deal a card to this player
            var dealingCardToResponse = new DealingCardToResponse
            {
                PlayerId = currentPlayer.Id,
                PlayerName = currentPlayer.Name,
                PlayerTeam = currentPlayer.Team,
                PlayerIndex = currentPlayerIndex
            };

            if (DealingCardTo != null)
            {
                await DealingCardTo(lobby.LobbyCode, dealingCardToResponse);
                if (matchedLobby != null)
                {
                    await DealingCardTo(matchedLobby.LobbyCode, dealingCardToResponse);
                }
            }

            // After a short delay, deal the actual card
            await Task.Delay(500);

            // Get the next card from the deck
            var card = lobby.DealerDeck.Last();
            lobby.DealerDeck.RemoveAt(lobby.DealerDeck.Count - 1);

            // Sync deck state with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.DealerDeck = new List<Card>(lobby.DealerDeck);
            }

            // Check if this is a black Jack (Jack of Clubs or Jack of Spades)
            var isBlackJack = card.Value == "J" && (card.Suit == "clubs" || card.Suit == "spades");

            // Increment the card count for this player
            if (!lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id))
                lobby.CardsDealtPerPlayer[currentPlayer.Id] = 0;
            lobby.CardsDealtPerPlayer[currentPlayer.Id]++;

            // Sync card count with matched lobby
            if (matchedLobby != null)
            {
                matchedLobby.CardsDealtPerPlayer = new Dictionary<string, int>(lobby.CardsDealtPerPlayer);
            }

            // Broadcast the card dealt to all players
            var cardDealtResponse = new CardDealtResponse
            {
                PlayerId = currentPlayer.Id,
                PlayerName = currentPlayer.Name,
                PlayerTeam = currentPlayer.Team,
                PlayerIndex = currentPlayerIndex,
                Card = card,
                IsDealer = isBlackJack,
                CardsDealtToPlayer = lobby.CardsDealtPerPlayer.GetValueOrDefault(currentPlayer.Id, 0),
                TotalCardsDealt = lobby.CardsDealtPerPlayer.Values.Sum()
            };

            if (CardDealt != null)
            {
                await CardDealt(lobby.LobbyCode, cardDealtResponse);
                if (matchedLobby != null)
                {
                    await CardDealt(matchedLobby.LobbyCode, cardDealtResponse);
                }
            }

            // If this is a black Jack, we found our dealer
            if (isBlackJack)
            {
                lobby.DealerFound = true;
                lobby.DealerId = currentPlayer.Id;

                // Sync dealer state with matched lobby
                if (matchedLobby != null)
                {
                    matchedLobby.DealerFound = true;
                    matchedLobby.DealerId = currentPlayer.Id;
                }

                // Assign fixed positions to players based on the dealer
                // This is crucial for determining the initial trumper correctly
                var positionedPlayers = PlayerPositionUtils.AssignPositionsBasedOnDealer(allPlayers, currentPlayer.Id);

                // Determine the trump selector using position-based logic
                // In Thunee, the trump selector is always at position 2 (to the right of dealer at position 3)
                var trumpSelectorPosition = PlayerPositionUtils.GetInitialTrumperPosition(3); // Dealer is always at position 3
                var trumpSelector = positionedPlayers.FirstOrDefault(p => p.Position == trumpSelectorPosition);

                // If position-based selection fails, fall back to team-based selection
                if (trumpSelector == null)
                {
                    var team = currentPlayer.Team;
                    var oppositeTeam = team == 1 ? 2 : 1;
                    var oppositeTeamPlayers = positionedPlayers.Where(p => p.Team == oppositeTeam).ToList();

                    // Choose the first player from the opposite team as trump selector
                    trumpSelector = oppositeTeamPlayers.FirstOrDefault();
                    if (trumpSelector == null)
                    {
                        // Fallback: use the dealer if no opposite team players
                        trumpSelector = currentPlayer;
                    }
                }

                // Update the players in the lobby with their assigned positions
                foreach (var player in lobby.Players)
                {
                    var positionedPlayer = positionedPlayers.FirstOrDefault(p => p.Id == player.Id);
                    if (positionedPlayer != null)
                    {
                        player.Position = positionedPlayer.Position;
                    }
                }

                // Also update matched lobby if it exists
                if (matchedLobby != null)
                {
                    foreach (var player in matchedLobby.Players)
                    {
                        var positionedPlayer = positionedPlayers.FirstOrDefault(p => p.Id == player.Id);
                        if (positionedPlayer != null)
                        {
                            player.Position = positionedPlayer.Position;
                        }
                    }
                }

                // Broadcast dealer found to all players
                var dealerFoundResponse = new DealerFoundResponse
                {
                    DealerId = currentPlayer.Id,
                    DealerName = currentPlayer.Name,
                    DealerTeam = currentPlayer.Team,
                    TrumpSelectorId = trumpSelector.Id,
                    TrumpSelectorName = trumpSelector.Name,
                    TrumpSelectorTeam = trumpSelector.Team,
                    DealerCard = card,
                    Players = positionedPlayers.Select(p => new Player
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Team = p.Team,
                        Position = p.Position,
                        IsDealer = p.Id == currentPlayer.Id,
                        IsTrumpSelector = p.Id == trumpSelector.Id
                    }).ToList()
                };

                // Wait a bit longer to show the card before announcing dealer
                await Task.Delay(2000);

                // Reset dealer determination in progress flag
                lobby.DealerDeterminationInProgress = false;
                if (matchedLobby != null)
                {
                    matchedLobby.DealerDeterminationInProgress = false;
                }

                if (DealerFound != null)
                {
                    await DealerFound(lobby.LobbyCode, dealerFoundResponse);
                    if (matchedLobby != null)
                    {
                        await DealerFound(matchedLobby.LobbyCode, dealerFoundResponse);
                    }
                }

                // After a delay, start the game (transition to shuffle phase)
                Console.WriteLine($"[DealNextCardAsync] Waiting 3 seconds before starting game transition...");
                await Task.Delay(3000);
                Console.WriteLine($"[DealNextCardAsync] About to call StartGameAfterDealerDeterminationAsync with dealer {currentPlayer.Id}");
                await StartGameAfterDealerDeterminationAsync(lobby, matchedLobby, positionedPlayers, currentPlayer.Id);
                Console.WriteLine($"[DealNextCardAsync] StartGameAfterDealerDeterminationAsync completed");

                return;
            }

            // Move to the next player in clockwise order
            lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;
            if (matchedLobby != null)
            {
                matchedLobby.CurrentDealerPlayerIndex = lobby.CurrentDealerPlayerIndex;
            }

            // Schedule the next card deal after a delay
            await Task.Delay(1500);
            await DealNextCardAsync(lobby, matchedLobby, allPlayers);
        }

        public async Task<bool> PassThuneeAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Mark that this player has passed Thunee
            if (lobby.ThuneePassedPlayers == null)
            {
                lobby.ThuneePassedPlayers = new List<string>();
            }

            if (!lobby.ThuneePassedPlayers.Contains(connectionId))
            {
                lobby.ThuneePassedPlayers.Add(connectionId);
            }

            // Check if all players have passed Thunee
            if (lobby.ThuneePassedPlayers.Count >= lobby.Players.Count)
            {
                // All players passed, mark Thunee opportunities as complete
                lobby.ThuneeOpportunitiesComplete = true;

                // Start the first player's turn with timer
                if (!string.IsNullOrEmpty(lobby.FirstPlayerId))
                {
                    await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, lobby.FirstPlayerId);
                }
            }

            return true;
        }

        public async Task<bool> HoldGameAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // HoldGame is essentially the same as calling Thunee but without specifying the first card
            // The player will play their first card and that card's suit becomes trump

            // Mark Thunee opportunities as complete
            lobby.ThuneeOpportunitiesComplete = true;

            // Set this player as the first to play
            lobby.FirstPlayerId = connectionId;

            // Start the player's turn with timer
            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, connectionId);

            return true;
        }

        private async Task ProcessHandCompletionAsync(Lobby lobby)
        {
            if (lobby.CurrentHandCards == null || lobby.CurrentHandCards.Count < 4) return;

            // Determine hand winner using proper card game logic
            var winnerCard = _cardService.DetermineHandWinner(lobby.CurrentHandCards, lobby.TrumpSuit);
            if (winnerCard?.PlayedBy == null) return;

            var winner = lobby.Players.FirstOrDefault(p => p.Id == winnerCard.PlayedBy);
            if (winner == null) return;

            // Calculate hand points
            var handPoints = lobby.CurrentHandCards.Sum(c => _cardService.GetCardPoints(c));

            // Create hand record
            var hand = new Hand
            {
                Id = lobby.CurrentHandId,
                Cards = lobby.CurrentHandCards,
                WinnerId = winner.Id,
                WinnerName = winner.Name,
                WinnerTeam = winner.Team,
                Points = handPoints
            };

            lobby.Hands.Add(hand);

            // Update ball points
            var teamKey = $"team{winner.Team}";
            if (!lobby.BallPoints.ContainsKey(teamKey))
            {
                lobby.BallPoints[teamKey] = 0;
            }
            lobby.BallPoints[teamKey] += handPoints;

            // Trigger hand completed event
            if (HandCompleted != null)
            {
                await HandCompleted(lobby.LobbyCode, new HandCompletedResponse
                {
                    HandId = lobby.CurrentHandId,
                    WinnerId = winner.Id,
                    WinnerName = winner.Name,
                    WinnerTeam = winner.Team,
                    HandCards = lobby.CurrentHandCards,
                    Points = handPoints,
                    NextPlayerId = winner.Id // Winner leads next hand
                });
            }

            // Reset for next hand
            lobby.CurrentHandCards = new List<Card>();
            lobby.CurrentHandId++;

            // Check if ball is complete (6 hands)
            if (lobby.Hands.Count >= 6)
            {
                await ProcessBallCompletionAsync(lobby);
            }
            else
            {
                // Set winner as next player to play
                await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, winner.Id);
            }
        }

        private async Task ProcessBallCompletionAsync(Lobby lobby)
        {
            await CompleteBallAsync(lobby.LobbyCode);
        }

        private async Task SetNextPlayerTurnAsync(Lobby lobby)
        {
            // Determine next player in turn order
            // This would use game logic to determine the next player

            // For now, just cycle through players (placeholder)
            var currentPlayerIndex = lobby.Players.FindIndex(p => p.Id == lobby.TurnTimerState?.CurrentPlayerId);
            var nextPlayerIndex = (currentPlayerIndex + 1) % lobby.Players.Count;
            var nextPlayer = lobby.Players[nextPlayerIndex];

            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, nextPlayer.Id);
        }

        // Helper methods for special calls
        private bool ValidateJordhiCards(List<Card> jordhiCards, string jordhiSuit, int value, string? trumpSuit)
        {
            if (jordhiCards == null || jordhiCards.Count == 0) return false;

            // Check if all cards are of the same suit
            if (!jordhiCards.All(c => c.Suit == jordhiSuit)) return false;

            // Validate based on Jordhi value
            switch (value)
            {
                case 20:
                    // K+Q (non-trump) or K+Q (trump)
                    return jordhiCards.Count >= 2 &&
                           jordhiCards.Any(c => c.Value == "K") &&
                           jordhiCards.Any(c => c.Value == "Q");
                case 40:
                    // K+Q+J (non-trump) or K+Q (trump)
                    if (jordhiSuit == trumpSuit)
                    {
                        return jordhiCards.Count >= 2 &&
                               jordhiCards.Any(c => c.Value == "K") &&
                               jordhiCards.Any(c => c.Value == "Q");
                    }
                    else
                    {
                        return jordhiCards.Count >= 3 &&
                               jordhiCards.Any(c => c.Value == "K") &&
                               jordhiCards.Any(c => c.Value == "Q") &&
                               jordhiCards.Any(c => c.Value == "J");
                    }
                case 50:
                    // K+Q+J (trump)
                    return jordhiSuit == trumpSuit &&
                           jordhiCards.Count >= 3 &&
                           jordhiCards.Any(c => c.Value == "K") &&
                           jordhiCards.Any(c => c.Value == "Q") &&
                           jordhiCards.Any(c => c.Value == "J");
                default:
                    return false;
            }
        }

        private int CountHandsWonByTeam(Lobby lobby, int team)
        {
            return lobby.Hands.Count(h => h.WinnerTeam == team);
        }

        private void AdjustTargetScoresForJordhi(Lobby lobby, int team, int jordhiValue)
        {
            if (lobby.TargetScores == null)
            {
                lobby.TargetScores = new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } };
            }

            // Reduce the target score for the team that called Jordhi
            var teamKey = $"team{team}";
            if (lobby.TargetScores.ContainsKey(teamKey))
            {
                lobby.TargetScores[teamKey] -= jordhiValue;
            }
        }

        // Helper methods for 4-ball validation
        private bool ValidateNeverFollowSuit(Lobby lobby, string accusedPlayerId, int handNumber)
        {
            // Find the specific hand
            var hand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            if (hand == null || hand.Cards.Count == 0) return false;

            // Find the accused player's card in this hand
            var accusedCard = hand.Cards.FirstOrDefault(c => c.PlayedBy == accusedPlayerId);
            if (accusedCard == null) return false;

            // Get the lead suit (first card played in the hand)
            var leadCard = hand.Cards.FirstOrDefault();
            if (leadCard == null) return false;

            var leadSuit = leadCard.Suit;

            // If the accused player played the lead suit, they followed suit correctly
            if (accusedCard.Suit == leadSuit) return false;

            // Check if the accused player had cards of the lead suit at the time of play
            // This would require tracking the player's hand state at each point in time
            // For now, we'll use a simplified validation
            if (lobby.PlayerCards.ContainsKey(accusedPlayerId))
            {
                var playerCards = lobby.PlayerCards[accusedPlayerId];
                var hasLeadSuit = playerCards.Any(c => c.Suit == leadSuit);

                // If they have cards of the lead suit now and didn't play it, it's a violation
                // Note: This is simplified - in a real game, we'd need to track historical hand states
                return hasLeadSuit;
            }

            return false;
        }

        private bool ValidateUnderChopped(Lobby lobby, string accusedPlayerId, int handNumber)
        {
            // Find the specific hand
            var hand = lobby.Hands.FirstOrDefault(h => h.Id == handNumber);
            if (hand == null || hand.Cards.Count == 0) return false;

            // Find the accused player's card in this hand
            var accusedCard = hand.Cards.FirstOrDefault(c => c.PlayedBy == accusedPlayerId);
            if (accusedCard == null) return false;

            // Get the lead suit (first card played in the hand)
            var leadCard = hand.Cards.FirstOrDefault();
            if (leadCard == null) return false;

            var leadSuit = leadCard.Suit;
            var trumpSuit = lobby.TrumpSuit;

            // Under chopping only applies when:
            // 1. Trump was not the lead suit
            // 2. Player didn't play highest trump
            // 3. Player did play a trump
            // 4. Player had non-trump cards available

            if (leadSuit == trumpSuit) return false; // Trump was lead suit
            if (accusedCard.Suit != trumpSuit) return false; // Player didn't play trump

            // Check if player had higher trump cards
            if (lobby.PlayerCards.ContainsKey(accusedPlayerId))
            {
                var playerCards = lobby.PlayerCards[accusedPlayerId];
                var trumpCards = playerCards.Where(c => c.Suit == trumpSuit).ToList();

                // Check if player had higher trump cards using custom trump ranking
                var hasHigherTrump = trumpCards.Any(c => IsHigherTrumpCard(c, accusedCard));

                // Check if player had non-trump cards
                var hasNonTrumpCards = playerCards.Any(c => c.Suit != trumpSuit);

                // Violation if they had higher trump and non-trump cards available
                return hasHigherTrump && hasNonTrumpCards;
            }

            return false;
        }

        private bool IsHigherTrumpCard(Card card1, Card card2)
        {
            // Custom trump ranking: Queen > King > 10 > Ace > 9 > Jack
            var trumpRanking = new Dictionary<string, int>
            {
                { "Q", 6 },
                { "K", 5 },
                { "10", 4 },
                { "A", 3 },
                { "9", 2 },
                { "J", 1 }
            };

            var rank1 = trumpRanking.GetValueOrDefault(card1.Value, 0);
            var rank2 = trumpRanking.GetValueOrDefault(card2.Value, 0);

            return rank1 > rank2;
        }





        // Ball completion logic
        public async Task<bool> CompleteBallAsync(string lobbyCode)
        {
            var lobby = _lobbyService.GetLobby(lobbyCode);
            if (lobby == null) return false;

            // Calculate ball scores
            var ballResult = CalculateBallResult(lobby);

            // Update ball scores
            var teamKey = $"team{ballResult.WinningTeam}";
            if (!lobby.BallScores.ContainsKey(teamKey))
            {
                lobby.BallScores[teamKey] = 0;
            }
            lobby.BallScores[teamKey] += ballResult.BallsAwarded;

            // Determine next dealer
            var nextDealer = DetermineNextDealer(lobby, ballResult.WinningTeam);

            // Create ball history record
            var ballHistory = new BallHistory
            {
                BallId = lobby.CurrentBallId,
                Winner = ballResult.WinningTeam,
                Points = ballResult.Points,
                NextDealer = nextDealer,
                BallScores = new Dictionary<string, int>(lobby.BallScores),
                FourBallAwarded = ballResult.FourBallAwarded,
                FourBallOption = ballResult.FourBallOption,
                FourBallWinningTeam = ballResult.FourBallWinningTeam,
                BallsAwarded = ballResult.BallsAwarded,
                Timestamp = DateTime.UtcNow
            };

            lobby.GameHistory.Add(ballHistory);

            // Trigger ball completed event
            if (BallCompleted != null)
            {
                await BallCompleted(lobbyCode, new BallCompletedResponse
                {
                    BallId = lobby.CurrentBallId,
                    Winner = ballResult.WinningTeam,
                    Points = ballResult.Points,
                    NextDealer = nextDealer,
                    BallScores = lobby.BallScores,
                    DoubleProcessed = ballResult.DoubleProcessed,
                    ThuneeSuccess = ballResult.ThuneeSuccess,
                    ThuneeFailure = ballResult.ThuneeFailure,
                    BallsAwarded = ballResult.BallsAwarded
                });
            }

            // Check if game is complete
            var gameWinner = CheckGameCompletion(lobby);
            if (gameWinner.HasValue)
            {
                await CompleteGameAsync(lobby, gameWinner.Value);
                return true;
            }

            // Reset for next ball
            ResetForNextBall(lobby, nextDealer);

            return true;
        }

        private BallResult CalculateBallResult(Lobby lobby)
        {
            var result = new BallResult();

            // Check for 4-ball penalties first (highest priority)
            if (lobby.FourBallCalls.Any(f => f.IsValid))
            {
                var validFourBall = lobby.FourBallCalls.First(f => f.IsValid);
                result.WinningTeam = validFourBall.PlayerTeam;
                result.BallsAwarded = 4;
                result.FourBallAwarded = true;
                result.FourBallOption = validFourBall.Option;
                result.FourBallWinningTeam = validFourBall.PlayerTeam;
                result.Points = new Dictionary<string, int> { { "team1", 0 }, { "team2", 0 } };
                return result;
            }

            // Check for Thunee calls
            if (lobby.ThuneeCalls.Any())
            {
                var thuneeCall = lobby.ThuneeCalls.First();
                var thuneeTeam = thuneeCall.PlayerTeam;
                var oppositeTeam = thuneeTeam == 1 ? 2 : 1;

                // Check if Thunee was successful (Thunee team won all hands)
                var thuneeTeamHands = CountHandsWonByTeam(lobby, thuneeTeam);
                var oppositeTeamHands = CountHandsWonByTeam(lobby, oppositeTeam);

                if (oppositeTeamHands == 0)
                {
                    // Thunee success - Thunee team gets 4 balls
                    result.WinningTeam = thuneeTeam;
                    result.BallsAwarded = 4;
                    result.ThuneeSuccess = true;
                }
                else
                {
                    // Thunee failure - Opposite team gets 4 balls
                    result.WinningTeam = oppositeTeam;
                    result.BallsAwarded = 4;
                    result.ThuneeFailure = true;
                }

                result.Points = new Dictionary<string, int> { { "team1", 0 }, { "team2", 0 } };
                return result;
            }

            // Normal ball scoring
            var team1Points = CalculateTeamPoints(lobby, 1);
            var team2Points = CalculateTeamPoints(lobby, 2);

            result.Points = new Dictionary<string, int>
            {
                { "team1", team1Points },
                { "team2", team2Points }
            };

            // Determine winner based on target scores
            var team1Target = lobby.TargetScores?.GetValueOrDefault("team1", 105) ?? 105;
            var team2Target = lobby.TargetScores?.GetValueOrDefault("team2", 105) ?? 105;

            var team1Reached = team1Points >= team1Target;
            var team2Reached = team2Points >= team2Target;

            if (team1Reached && team2Reached)
            {
                // Both teams reached target - higher score wins
                result.WinningTeam = team1Points > team2Points ? 1 : 2;
            }
            else if (team1Reached)
            {
                result.WinningTeam = 1;
            }
            else if (team2Reached)
            {
                result.WinningTeam = 2;
            }
            else
            {
                // Neither team reached target - higher score wins
                result.WinningTeam = team1Points > team2Points ? 1 : 2;
            }

            // Check for Double call
            if (lobby.DoubleCalls.Any(d => d.IsValid))
            {
                var doubleCall = lobby.DoubleCalls.First(d => d.IsValid);
                var doubleTeam = doubleCall.PlayerTeam;

                if (result.WinningTeam == doubleTeam)
                {
                    result.BallsAwarded = 2; // Double successful
                    result.DoubleProcessed = true;
                }
                else
                {
                    result.BallsAwarded = 1; // Normal ball
                }
            }
            else
            {
                result.BallsAwarded = 1; // Normal ball
            }

            return result;
        }

        private int CalculateTeamPoints(Lobby lobby, int team)
        {
            var teamHands = lobby.Hands.Where(h => h.WinnerTeam == team);
            var totalPoints = teamHands.Sum(h => h.Points);

            // Add Jordhi points
            var jordhiPoints = lobby.JordhiCalls
                .Where(j => j.PlayerTeam == team && j.IsFullyValid)
                .Sum(j => j.Value);

            return totalPoints + jordhiPoints;
        }

        private string DetermineNextDealer(Lobby lobby, int ballWinningTeam)
        {
            // Thunee dealer rotation rules:
            // - If the team with the current dealer is ahead or tied, deal passes to the right
            // - If the team with the current dealer is behind, same dealer deals again

            var currentDealer = lobby.Players.FirstOrDefault(p => p.IsDealer);
            if (currentDealer == null) return lobby.Players.First().Id;

            var currentDealerTeam = currentDealer.Team;
            var currentDealerTeamScore = lobby.BallScores.GetValueOrDefault($"team{currentDealerTeam}", 0);
            var otherTeam = currentDealerTeam == 1 ? 2 : 1;
            var otherTeamScore = lobby.BallScores.GetValueOrDefault($"team{otherTeam}", 0);

            if (currentDealerTeamScore >= otherTeamScore)
            {
                // Deal passes to the right (next player in rotation)
                var currentIndex = lobby.Players.FindIndex(p => p.Id == currentDealer.Id);
                var nextIndex = (currentIndex + 1) % lobby.Players.Count;
                return lobby.Players[nextIndex].Id;
            }
            else
            {
                // Same dealer deals again
                return currentDealer.Id;
            }
        }

        private int? CheckGameCompletion(Lobby lobby)
        {
            // Game ends when a team reaches 12 balls (or 13 with winning Khanak)
            var team1Balls = lobby.BallScores.GetValueOrDefault("team1", 0);
            var team2Balls = lobby.BallScores.GetValueOrDefault("team2", 0);

            // Check for Khanak win (13 balls)
            if (lobby.KhanakCalls.Any(k => k.IsValid))
            {
                if (team1Balls >= 13) return 1;
                if (team2Balls >= 13) return 2;
            }

            // Normal win (12 balls)
            if (team1Balls >= 12) return 1;
            if (team2Balls >= 12) return 2;

            return null;
        }

        private async Task CompleteGameAsync(Lobby lobby, int winningTeam)
        {
            // Trigger game ended event
            if (GameEnded != null)
            {
                await GameEnded(lobby.LobbyCode, new GameEndedResponse
                {
                    WinningTeam = winningTeam,
                    FinalBallScores = lobby.BallScores,
                    GameHistory = lobby.GameHistory,
                    Reason = "Game completed",
                    Winner = winningTeam,
                    FinalScores = lobby.BallScores,
                    BallLimit = lobby.KhanakCalls.Any(k => k.IsValid) ? 13 : 12,
                    HasWinningKhanak = lobby.KhanakCalls.Any(k => k.IsValid && k.PlayerTeam == winningTeam)
                });
            }
        }

        private async void ResetForNextBall(Lobby lobby, string nextDealerId)
        {
            // Reset ball-specific state
            lobby.CurrentBallId++;
            lobby.CurrentHandId = 1;
            lobby.Hands.Clear();
            lobby.PlayerCards.Clear();
            lobby.JordhiCalls.Clear();
            lobby.DoubleCalls.Clear();
            lobby.KhanakCalls.Clear();
            lobby.ThuneeCalls.Clear();
            lobby.FourBallCalls.Clear();
            lobby.ThuneePassedPlayers.Clear();
            lobby.ThuneeOpportunitiesComplete = false;
            lobby.TrumpSuit = null;
            lobby.FirstPlayerId = null;
            lobby.DoubleCallerId = null;
            lobby.KhanakCallerId = null;

            // Update dealer
            foreach (var player in lobby.Players)
            {
                player.IsDealer = player.Id == nextDealerId;
                player.IsTrumpSelector = false;
            }

            // Reset target scores
            lobby.TargetScores = new Dictionary<string, int> { { "team1", 105 }, { "team2", 105 } };

            // After a delay, transition to shuffle phase for next ball
            _ = Task.Delay(15000).ContinueWith(async _ =>
            {
                if (GamePhaseUpdated != null)
                {
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "shuffle",
                        Players = lobby.Players
                    });
                }
            });
        }

        private object CreateGameHistoryResponse(Lobby lobby)
        {
            return new
            {
                balls = lobby.GameHistory.Select(b => new
                {
                    ballNumber = b.BallId,
                    winner = b.Winner,
                    ballsAwarded = b.BallsAwarded,
                    points = b.Points,
                    ballScores = b.BallScores,
                    timestamp = b.Timestamp.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    specialCall = b.FourBallAwarded ? "4-Ball" : null,
                    details = new { }
                }).ToArray(),
                startTime = lobby.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                endTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                totalBalls = lobby.GameHistory.Count,
                duration = (int)(DateTime.UtcNow - lobby.CreatedAt).TotalMinutes
            };
        }



        private async Task StartGameAfterDealerDeterminationAsync(Lobby lobby, Lobby? matchedLobby, List<Player> allPlayers, string dealerId)
        {
            try
            {
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Starting game with dealer {dealerId}");

                // Mark both lobbies as started
                lobby.GameStarted = true;
                if (matchedLobby != null)
                {
                    matchedLobby.GameStarted = true;
                }

                // Set the dealer in both lobbies
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Setting DealerId to {dealerId} in lobby {lobby.LobbyCode}");
                lobby.DealerId = dealerId;
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] DealerId set to: {lobby.DealerId}");

                if (matchedLobby != null)
                {
                    Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Setting DealerId to {dealerId} in matched lobby {matchedLobby.LobbyCode}");
                    matchedLobby.DealerId = dealerId;
                    Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Matched lobby DealerId set to: {matchedLobby.DealerId}");
                }

                // Find the dealer player
                var dealer = allPlayers.FirstOrDefault(p => p.Id == dealerId);
                if (dealer == null)
                {
                    Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] ERROR: Dealer with ID {dealerId} not found in players list");
                    return;
                }

                // Mark the dealer in the player list
                dealer.IsDealer = true;

                // Update the players in both lobbies with their assigned positions
                foreach (var player in lobby.Players)
                {
                    var positionedPlayer = allPlayers.FirstOrDefault(p => p.Id == player.Id);
                    if (positionedPlayer != null)
                    {
                        player.Position = positionedPlayer.Position;
                        player.IsDealer = positionedPlayer.IsDealer;
                        player.IsTrumpSelector = positionedPlayer.IsTrumpSelector;
                    }
                }

                if (matchedLobby != null)
                {
                    foreach (var player in matchedLobby.Players)
                    {
                        var positionedPlayer = allPlayers.FirstOrDefault(p => p.Id == player.Id);
                        if (positionedPlayer != null)
                        {
                            player.Position = positionedPlayer.Position;
                            player.IsDealer = positionedPlayer.IsDealer;
                            player.IsTrumpSelector = positionedPlayer.IsTrumpSelector;
                        }
                    }
                }

                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] About to send GamePhaseUpdated event to shuffle phase");
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Players being sent to clients: " +
                    string.Join(", ", allPlayers.Select(p => $"{p.Name} ({p.Id}) - Team {p.Team} - Position {p.Position}")));

                // Notify all players in both lobbies that the game is moving to shuffle phase
                if (GamePhaseUpdated != null)
                {
                    Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Sending GamePhaseUpdated to lobby {lobby.LobbyCode}");
                    await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                    {
                        Phase = "shuffle",
                        Players = allPlayers
                    });

                    if (matchedLobby != null && matchedLobby.LobbyCode != lobby.LobbyCode)
                    {
                        Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Sending GamePhaseUpdated to matched lobby {matchedLobby.LobbyCode}");
                        await GamePhaseUpdated(matchedLobby.LobbyCode, new GamePhaseUpdatedResponse
                        {
                            Phase = "shuffle",
                            Players = allPlayers
                        });
                    }
                }
                else
                {
                    Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] ERROR: GamePhaseUpdated event is null!");
                }

                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Game phase updated to shuffle for lobbies {lobby.LobbyCode}" +
                    (matchedLobby != null && matchedLobby.LobbyCode != lobby.LobbyCode ? $" and {matchedLobby.LobbyCode}" : ""));

                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Game started between lobbies with dealer {dealer.Name}");
            }
            catch (Exception error)
            {
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] ERROR: {error}");
                Console.WriteLine($"[StartGameAfterDealerDeterminationAsync] Stack trace: {error.StackTrace}");
            }
        }

        // Bidding helper methods
        private async Task InitializeBiddingState(Lobby lobby)
        {
            try
            {
                Console.WriteLine($"[InitializeBiddingState] Initializing bidding state for lobby {lobby.LobbyCode}");

                lobby.BiddingState = new BiddingState
                {
                    CurrentBid = 0,
                    CurrentBidder = null,
                    HighestBidder = null,
                    BiddingComplete = false,
                    TeamBids = new Dictionary<int, int>(),
                    TeamBidders = new Dictionary<int, string>(),
                    BiddingOrder = new List<string>(),
                    CurrentBidderIndex = 0,
                    PassedPlayers = new HashSet<string>(),
                    TeamsAllowedToBid = new HashSet<int>(),
                    TeamsThatBid = new HashSet<int>()
                };

                Console.WriteLine($"[InitializeBiddingState] BiddingState object created successfully");

            // Set bidding order according to Thunee rules - EXACTLY like Node.js server
            var dealer = lobby.Players.FirstOrDefault(p => p.IsDealer);
            if (dealer == null)
            {
                Console.WriteLine($"[InitializeBiddingState] ERROR: No dealer found");
                return;
            }

            var dealerTeam = dealer.Team;

            // Initially, only dealer's team is allowed to bid
            lobby.BiddingState.TeamsAllowedToBid.Add(dealerTeam);

            // Create bidding order: Dealer first, then dealer's partner, then opposing team
            var allPlayers = lobby.Players.OrderBy(p => p.Position).ToList();

            // Find dealer and add them first
            lobby.BiddingState.BiddingOrder.Add(dealer.Id);

            // Find dealer's partner and add them second
            var dealerPartner = allPlayers.FirstOrDefault(p => p.Team == dealerTeam && p.Id != dealer.Id);
            if (dealerPartner != null)
            {
                lobby.BiddingState.BiddingOrder.Add(dealerPartner.Id);
            }

            // Add opposing team players (right of dealer first - "initial trumper")
            var opposingTeamPlayers = allPlayers.Where(p => p.Team != dealerTeam).OrderBy(p => p.Position).ToList();
            foreach (var player in opposingTeamPlayers)
            {
                lobby.BiddingState.BiddingOrder.Add(player.Id);
            }

            Console.WriteLine($"[InitializeBiddingState] Bidding order: {string.Join(" -> ", lobby.BiddingState.BiddingOrder.Select(id => lobby.Players.FirstOrDefault(p => p.Id == id)?.Name ?? "Unknown"))}");
            Console.WriteLine($"[InitializeBiddingState] Initially only team {dealerTeam} (dealer's team) can bid");

            // Start with first bidder (dealer)
            lobby.BiddingState.CurrentBidderIndex = 0;

            // Start 3-second timer for first bidder
            await StartBiddingTimer(lobby, dealer.Id);

            Console.WriteLine($"[InitializeBiddingState] Starting bidding phase with dealer: {dealer.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[InitializeBiddingState] ERROR: {ex.Message}");
                Console.WriteLine($"[InitializeBiddingState] Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        private async Task MoveToNextBidder(Lobby lobby)
        {
            Console.WriteLine($"[MoveToNextBidder] Moving to next bidder");

            // Check if bidding is complete
            if (lobby.BiddingState.PassedPlayers.Count >= lobby.BiddingState.BiddingOrder.Count - 1)
            {
                Console.WriteLine($"[MoveToNextBidder] Only one player left, bidding is complete");
                await CompleteBidding(lobby);
                return;
            }

            var attempts = 0;
            var nextBidderFound = false;

            while (!nextBidderFound && attempts < lobby.BiddingState.BiddingOrder.Count)
            {
                // Move to the next player in the bidding order
                lobby.BiddingState.CurrentBidderIndex = (lobby.BiddingState.CurrentBidderIndex + 1) % lobby.BiddingState.BiddingOrder.Count;

                var nextBidderId = lobby.BiddingState.BiddingOrder[lobby.BiddingState.CurrentBidderIndex];
                var nextBidder = lobby.Players.FirstOrDefault(p => p.Id == nextBidderId);

                if (nextBidder == null)
                {
                    Console.WriteLine($"[MoveToNextBidder] Next bidder {nextBidderId} not found in players list");
                    attempts++;
                    continue;
                }

                // Check if this player has already passed
                if (lobby.BiddingState.PassedPlayers.Contains(nextBidderId))
                {
                    Console.WriteLine($"[MoveToNextBidder] Player {nextBidder.Name} has already passed, skipping");
                    attempts++;
                    continue;
                }

                // CRITICAL: Check team-based bidding rules like Node.js server
                var playerTeam = nextBidder.Team;

                // Check if this team is allowed to bid
                if (!lobby.BiddingState.TeamsAllowedToBid.Contains(playerTeam))
                {
                    Console.WriteLine($"[MoveToNextBidder] Team {playerTeam} is not allowed to bid yet, skipping player {nextBidder.Name}");
                    attempts++;
                    continue;
                }

                // Check if this team has already bid (only one player per team can bid)
                if (lobby.BiddingState.TeamsThatBid.Contains(playerTeam))
                {
                    Console.WriteLine($"[MoveToNextBidder] Team {playerTeam} has already bid, skipping player {nextBidder.Name}");
                    attempts++;
                    continue;
                }

                // This player can bid
                nextBidderFound = true;
                Console.WriteLine($"[MoveToNextBidder] Player {nextBidder.Name} (Team {playerTeam}) can bid");

                // Start 3-second timer for this bidder
                await StartBiddingTimer(lobby, nextBidderId);

                attempts++;
            }

            // If we couldn't find a next bidder, complete bidding
            if (!nextBidderFound)
            {
                Console.WriteLine($"[MoveToNextBidder] No next bidder found, completing bidding");
                await CompleteBidding(lobby);
            }
        }

        // Bidding timer functionality (3 seconds like Node.js server)
        private readonly Dictionary<string, Timer> _biddingTimers = new();

        private async Task StartBiddingTimer(Lobby lobby, string playerId)
        {
            Console.WriteLine($"[StartBiddingTimer] Starting 3-second timer for player {playerId}");

            // Stop any existing timer for this lobby
            StopBiddingTimer(lobby);

            // Notify player it's their turn to bid
            if (YourTurnToBid != null)
            {
                await YourTurnToBid(lobby.LobbyCode, new YourTurnToBidResponse
                {
                    PlayerId = playerId
                });
            }

            // Send bidding state update to all players
            var player = lobby.Players.FirstOrDefault(p => p.Id == playerId);
            if (BiddingStateUpdated != null && player != null)
            {
                await BiddingStateUpdated(lobby.LobbyCode, new BiddingStateUpdatedResponse
                {
                    CurrentBidder = playerId,
                    CurrentBidderName = player.Name,
                    CurrentBidderTeam = player.Team,
                    HighestBid = lobby.BiddingState.CurrentBid,
                    HighestBidder = lobby.BiddingState.HighestBidder
                });
            }

            // Create 3-second timer
            var timer = new Timer(_ =>
            {
                Console.WriteLine($"[StartBiddingTimer] Timer expired for player {playerId}, auto-passing");
                _ = Task.Run(async () => await HandleBiddingTimeout(lobby, playerId));
            }, null, TimeSpan.FromSeconds(3), Timeout.InfiniteTimeSpan);

            _biddingTimers[lobby.LobbyCode] = timer;
        }

        private void StopBiddingTimer(Lobby lobby)
        {
            if (_biddingTimers.TryGetValue(lobby.LobbyCode, out var timer))
            {
                timer?.Dispose();
                _biddingTimers.Remove(lobby.LobbyCode);
                Console.WriteLine($"[StopBiddingTimer] Stopped bidding timer for lobby {lobby.LobbyCode}");
            }
        }

        private async Task HandleBiddingTimeout(Lobby lobby, string playerId)
        {
            try
            {
                Console.WriteLine($"[HandleBiddingTimeout] Handling timeout for player {playerId}");

                // Check if this player is still the current bidder
                if (lobby.BiddingState == null || lobby.BiddingState.BiddingComplete)
                {
                    Console.WriteLine($"[HandleBiddingTimeout] Bidding already complete, ignoring timeout");
                    return;
                }

                var currentBidder = lobby.BiddingState.BiddingOrder[lobby.BiddingState.CurrentBidderIndex];
                if (currentBidder != playerId)
                {
                    Console.WriteLine($"[HandleBiddingTimeout] Player {playerId} is no longer current bidder, ignoring timeout");
                    return;
                }

                var player = lobby.Players.FirstOrDefault(p => p.Id == playerId);
                if (player == null)
                {
                    Console.WriteLine($"[HandleBiddingTimeout] Player {playerId} not found");
                    return;
                }

                // Auto-pass the player
                lobby.BiddingState.PassedPlayers.Add(playerId);

                Console.WriteLine($"[HandleBiddingTimeout] Auto-passed player {player.Name} due to timeout");

                // Broadcast auto-pass to all players
                if (BidPassed != null)
                {
                    await BidPassed(lobby.LobbyCode, new BidPassedResponse
                    {
                        PlayerId = playerId,
                        PlayerName = player.Name,
                        AutoPass = true,
                        Reason = "Time expired"
                    });
                }

                // Move to next bidder
                await MoveToNextBidder(lobby);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[HandleBiddingTimeout] Error handling timeout: {ex.Message}");
            }
        }

        private async Task CompleteBidding(Lobby lobby)
        {
            Console.WriteLine($"[CompleteBidding] Bidding complete");

            // Stop any active bidding timer
            StopBiddingTimer(lobby);

            lobby.BiddingState.BiddingComplete = true;

            // Determine trump selector (highest bidder or default)
            string trumpSelectorId;
            string trumpSelectorName;
            int trumpSelectorTeam;
            int finalBid;
            bool isDefaultTrumper = false;

            if (lobby.BiddingState.HighestBidder != null)
            {
                // Someone made a bid
                trumpSelectorId = lobby.BiddingState.HighestBidder;
                var trumpSelector = lobby.Players.FirstOrDefault(p => p.Id == trumpSelectorId);
                trumpSelectorName = trumpSelector?.Name ?? "Unknown";
                trumpSelectorTeam = trumpSelector?.Team ?? 1;
                finalBid = lobby.BiddingState.CurrentBid;

                Console.WriteLine($"[CompleteBidding] Highest bidder: {trumpSelectorName} (Team {trumpSelectorTeam}) with bid {finalBid}");
            }
            else
            {
                // No bids made, dealer's team becomes trumper by default
                var dealer = lobby.Players.FirstOrDefault(p => p.IsDealer);
                if (dealer == null)
                {
                    Console.WriteLine($"[CompleteBidding] ERROR: No dealer found");
                    return;
                }

                trumpSelectorId = dealer.Id;
                trumpSelectorName = dealer.Name;
                trumpSelectorTeam = dealer.Team;
                finalBid = 0;
                isDefaultTrumper = true;

                Console.WriteLine($"[CompleteBidding] No bids made, dealer {trumpSelectorName} (Team {trumpSelectorTeam}) becomes default trumper");
            }

            // Set trump selector
            lobby.TrumpSelectorId = trumpSelectorId;

            // Update target scores
            if (finalBid > 0)
            {
                var opposingTeam = trumpSelectorTeam == 1 ? 2 : 1;
                lobby.TargetScores = new Dictionary<string, int>
                {
                    { $"team{opposingTeam}", 105 - finalBid }
                };

                Console.WriteLine($"[CompleteBidding] Target score for team {opposingTeam}: {105 - finalBid}");
            }
            else
            {
                // Default target scores
                lobby.TargetScores = new Dictionary<string, int>
                {
                    { "team1", 105 },
                    { "team2", 105 }
                };

                Console.WriteLine($"[CompleteBidding] Default target scores: 105 for both teams");
            }

            // Trigger bidding complete event
            if (BiddingCompleted != null)
            {
                await BiddingCompleted(lobby.LobbyCode, new BiddingCompletedResponse
                {
                    HighestBid = finalBid,
                    HighestBidder = trumpSelectorId,
                    TrumpSelectorId = trumpSelectorId,
                    TargetScores = lobby.TargetScores
                });
            }

            // Update game phase to trump selection
            if (GamePhaseUpdated != null)
            {
                await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                {
                    Phase = "select-trump",
                    Players = lobby.Players
                });
            }

            Console.WriteLine($"[CompleteBidding] Bidding completed, moving to trump selection phase");
        }

        // Helper class for ball result calculation
        private class BallResult
        {
            public int WinningTeam { get; set; }
            public int BallsAwarded { get; set; } = 1;
            public Dictionary<string, int> Points { get; set; } = new();
            public bool FourBallAwarded { get; set; }
            public string? FourBallOption { get; set; }
            public int? FourBallWinningTeam { get; set; }
            public bool DoubleProcessed { get; set; }
            public bool ThuneeSuccess { get; set; }
            public bool ThuneeFailure { get; set; }
        }
    }
}
